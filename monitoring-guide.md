# Uptime Kuma 本机服务器监控配置指南

## 🎯 监控目标

基于您服务器的实际情况，建议添加以下监控项：

### 1. HTTP/HTTPS 监控

#### Nginx Web服务器监控
- **监控类型**: HTTP(s)
- **URL**: `http://localhost` 或 `http://************`
- **名称**: "本机Nginx服务"
- **检查间隔**: 60秒
- **超时时间**: 48秒
- **重试次数**: 1

#### HTTPS监控（如果配置了SSL）
- **监控类型**: HTTP(s)
- **URL**: `https://************`
- **名称**: "本机HTTPS服务"

### 2. TCP端口监控

#### SSH服务监控
- **监控类型**: Port
- **主机名**: `localhost` 或 `127.0.0.1`
- **端口**: `22`
- **名称**: "SSH服务"

#### Docker服务监控
- **监控类型**: Port
- **主机名**: `localhost`
- **端口**: `8080`
- **名称**: "Docker服务"

#### 1Panel管理面板监控
- **监控类型**: Port
- **主机名**: `localhost`
- **端口**: `22772`
- **名称**: "1Panel管理面板"

### 3. Ping监控

#### 本机连通性监控
- **监控类型**: Ping
- **主机名**: `127.0.0.1`
- **名称**: "本机Ping测试"

## 📋 详细添加步骤

### 步骤1: 登录Uptime Kuma
1. 访问 http://************:3001
2. 首次访问创建管理员账户
3. 登录到管理界面

### 步骤2: 添加HTTP监控
1. 点击 "Add New Monitor"
2. 选择监控类型: "HTTP(s)"
3. 填写配置:
   - **Friendly Name**: "本机Nginx服务"
   - **URL**: `http://localhost`
   - **Heartbeat Interval**: 60 seconds
   - **Max Retries**: 1
   - **Timeout**: 48 seconds
4. 点击 "Save"

### 步骤3: 添加端口监控
1. 点击 "Add New Monitor"
2. 选择监控类型: "Port"
3. 填写配置:
   - **Friendly Name**: "SSH服务"
   - **Hostname**: `localhost`
   - **Port**: `22`
   - **Heartbeat Interval**: 60 seconds
4. 点击 "Save"

### 步骤4: 添加Ping监控
1. 点击 "Add New Monitor"
2. 选择监控类型: "Ping"
3. 填写配置:
   - **Friendly Name**: "本机连通性"
   - **Hostname**: `127.0.0.1`
   - **Heartbeat Interval**: 60 seconds
4. 点击 "Save"

## 🔧 高级配置选项

### 关键词监控
对于HTTP监控，可以添加关键词检查：
- **Expected Keywords**: "Welcome to nginx!"
- 确保页面内容正确

### 状态码检查
- **Accepted Status Codes**: 200-299
- 只接受成功的HTTP状态码

### 通知设置
1. 进入 "Settings" → "Notifications"
2. 配置邮件、Webhook或其他通知方式
3. 将通知绑定到监控项

## 📊 推荐的监控组合

### 基础监控套餐
1. HTTP监控 - Nginx服务
2. Port监控 - SSH服务
3. Ping监控 - 本机连通性

### 完整监控套餐
1. HTTP监控 - Nginx (端口80)
2. HTTPS监控 - SSL服务 (端口443)
3. Port监控 - SSH (端口22)
4. Port监控 - Docker (端口8080)
5. Port监控 - 1Panel (端口22772)
6. Ping监控 - 本机连通性

## ⚠️ 注意事项

1. **使用localhost**: 监控本机服务时使用 `localhost` 或 `127.0.0.1`
2. **防火墙**: 确保Uptime Kuma容器可以访问被监控的端口
3. **权限**: 某些端口可能需要特殊权限才能访问
4. **资源消耗**: 不要设置过于频繁的检查间隔

## 🚨 故障排除

### 常见问题
1. **连接被拒绝**: 检查服务是否运行，端口是否正确
2. **超时**: 增加超时时间或检查网络连接
3. **权限问题**: 确保Uptime Kuma有访问权限
