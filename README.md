# Uptime Kuma 监控面板部署

## 简介
Uptime Kuma 是一个自托管的监控工具，类似于 "Uptime Robot"。

## 部署说明

### 启动服务
```bash
docker compose up -d
```

### 停止服务
```bash
docker compose down
```

### 查看日志
```bash
docker compose logs -f uptime-kuma
```

### 访问面板
- 访问地址：http://your-server-ip:3001
- 首次访问时需要创建管理员账户

## 配置说明
- 数据存储：使用Docker volume `uptime-kuma` 持久化数据
- 端口：默认使用3001端口，可在docker-compose.yml中修改
- 重启策略：容器会自动重启（除非手动停止）

## 安全建议
1. 建议使用反向代理（如Nginx）并配置SSL证书
2. 可以修改默认端口以增加安全性
3. 定期备份数据卷

## 数据备份
```bash
# 备份数据
docker run --rm -v uptime-kuma:/data -v $(pwd):/backup alpine tar czf /backup/uptime-kuma-backup.tar.gz -C /data .

# 恢复数据
docker run --rm -v uptime-kuma:/data -v $(pwd):/backup alpine tar xzf /backup/uptime-kuma-backup.tar.gz -C /data
```
