# 🔧 Uptime Kuma 连接问题修复总结

## ❌ 原始问题

您遇到的错误：
```
[yuxin] [DOWN] connect ECONNREFUSED 127.0.0.1:22
[本机web服务] [DOWN] connect ECONNREFUSED 127.0.0.1:80
```

## 🔍 问题原因

**根本原因**：Docker容器网络隔离
- Uptime Kuma运行在独立的Docker网络中
- 容器内的 `localhost` 指向容器本身，而不是宿主机
- 无法访问宿主机上的服务（如Nginx、SSH等）

## ✅ 解决方案

**修改Docker Compose配置**：
- 从桥接网络模式改为 `host` 网络模式
- 移除端口映射（因为使用host网络）
- 现在容器可以直接访问宿主机的所有服务

### 修改前的配置：
```yaml
services:
  uptime-kuma:
    ports:
      - "3001:3001"
    # 使用默认桥接网络
```

### 修改后的配置：
```yaml
services:
  uptime-kuma:
    network_mode: host  # 使用宿主机网络
    # 移除端口映射
```

## 🎯 修复验证

✅ **HTTP服务测试**：容器内可以访问 `http://localhost`，返回200状态码
✅ **Ping测试**：容器内可以ping通 `127.0.0.1`
✅ **服务重启**：Uptime Kuma已成功重启并运行

## 📱 现在可以正常配置监控

### 推荐的监控配置：

1. **HTTP监控**
   - URL: `http://localhost`
   - 名称: "本机Web服务"

2. **HTTPS监控**
   - URL: `https://localhost`
   - 名称: "本机HTTPS服务"

3. **端口监控 - SSH**
   - 主机: `localhost`
   - 端口: `22`

4. **端口监控 - Docker**
   - 主机: `localhost`
   - 端口: `8080`

5. **Ping监控**
   - 主机: `127.0.0.1`

## 🌐 访问地址

Uptime Kuma管理界面：http://************:3001

## 💡 技术说明

**Host网络模式的优势**：
- 容器直接使用宿主机网络栈
- 无需端口映射
- 可以访问宿主机的所有服务
- 性能更好（减少网络层转换）

**注意事项**：
- 容器端口直接暴露在宿主机上
- 需要确保端口不冲突
- 安全性需要额外考虑

现在您可以重新在Uptime Kuma中添加监控项了，应该不会再出现连接被拒绝的错误！
