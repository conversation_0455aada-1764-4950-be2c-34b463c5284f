#!/bin/bash

# Uptime Kuma 本机服务测试脚本
# 用于验证各项服务是否可以被监控

echo "🔍 测试本机服务可访问性..."
echo "=================================="

# 测试HTTP服务
echo "📡 测试HTTP服务 (端口80):"
if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200"; then
    echo "✅ HTTP服务正常 - 可以添加HTTP监控"
else
    echo "❌ HTTP服务异常"
fi

# 测试HTTPS服务
echo "🔒 测试HTTPS服务 (端口443):"
if curl -s -k -o /dev/null -w "%{http_code}" https://localhost 2>/dev/null | grep -q "200"; then
    echo "✅ HTTPS服务正常 - 可以添加HTTPS监控"
else
    echo "⚠️  HTTPS服务未配置或异常"
fi

# 测试SSH端口
echo "🔑 测试SSH服务 (端口22):"
if nc -z localhost 22 2>/dev/null; then
    echo "✅ SSH端口开放 - 可以添加Port监控"
else
    echo "❌ SSH端口不可访问"
fi

# 测试Docker端口
echo "🐳 测试Docker服务 (端口8080):"
if nc -z localhost 8080 2>/dev/null; then
    echo "✅ Docker端口开放 - 可以添加Port监控"
else
    echo "❌ Docker端口不可访问"
fi

# 测试1Panel端口
echo "🎛️  测试1Panel服务 (端口22772):"
if nc -z localhost 22772 2>/dev/null; then
    echo "✅ 1Panel端口开放 - 可以添加Port监控"
else
    echo "❌ 1Panel端口不可访问"
fi

# 测试Ping
echo "🏓 测试Ping连通性:"
if ping -c 1 127.0.0.1 >/dev/null 2>&1; then
    echo "✅ Ping测试成功 - 可以添加Ping监控"
else
    echo "❌ Ping测试失败"
fi

echo "=================================="
echo "🎯 推荐的Uptime Kuma监控配置:"
echo ""
echo "1. HTTP监控:"
echo "   - URL: http://localhost"
echo "   - 名称: 本机Nginx服务"
echo ""
echo "2. Port监控 - SSH:"
echo "   - 主机: localhost"
echo "   - 端口: 22"
echo ""
echo "3. Port监控 - Docker:"
echo "   - 主机: localhost" 
echo "   - 端口: 8080"
echo ""
echo "4. Ping监控:"
echo "   - 主机: 127.0.0.1"
echo ""
echo "📖 详细配置请参考 monitoring-guide.md 文件"
