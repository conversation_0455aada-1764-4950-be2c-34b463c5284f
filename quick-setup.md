# 🚀 Uptime Kuma 本机监控快速设置

## 🔧 问题已修复！

✅ **网络配置已优化**：已将Uptime Kuma配置为使用host网络模式，现在可以正常访问本机服务了！

## 📋 测试结果总结

✅ **所有服务测试通过！** 您可以添加以下监控项：

### 🌐 可监控的服务列表

| 服务名称 | 类型 | 地址/端口 | 状态 |
|---------|------|-----------|------|
| Nginx HTTP | HTTP | http://localhost | ✅ 正常 |
| Nginx HTTPS | HTTPS | https://localhost | ✅ 正常 |
| SSH服务 | Port | localhost:22 | ✅ 开放 |
| Docker服务 | Port | localhost:8080 | ✅ 开放 |
| 1Panel管理 | Port | localhost:22772 | ✅ 开放 |
| 本机连通性 | Ping | 127.0.0.1 | ✅ 正常 |

## 🎯 推荐的监控配置

### 1. 基础监控套餐（推荐新手）

#### HTTP监控 - Nginx服务
```
监控类型: HTTP(s)
友好名称: 本机Web服务
URL: http://localhost
心跳间隔: 60秒
超时时间: 48秒
重试次数: 1
```

#### 端口监控 - SSH服务
```
监控类型: Port
友好名称: SSH服务
主机名: localhost
端口: 22
心跳间隔: 60秒
```

#### Ping监控 - 连通性
```
监控类型: Ping
友好名称: 本机连通性
主机名: 127.0.0.1
心跳间隔: 60秒
```

### 2. 完整监控套餐（推荐高级用户）

在基础套餐基础上，额外添加：

#### HTTPS监控
```
监控类型: HTTP(s)
友好名称: 本机HTTPS服务
URL: https://localhost
忽略TLS错误: 是（如果使用自签名证书）
```

#### Docker服务监控
```
监控类型: Port
友好名称: Docker服务
主机名: localhost
端口: 8080
```

#### 1Panel管理面板监控
```
监控类型: Port
友好名称: 1Panel管理面板
主机名: localhost
端口: 22772
```

## 📱 操作步骤

### 第一步：访问管理界面
1. 打开浏览器
2. 访问：http://************:3001
3. 首次访问需要创建管理员账户

### 第二步：添加监控项
1. 点击右上角 "➕ Add New Monitor"
2. 选择监控类型
3. 填写配置信息
4. 点击 "Save" 保存

### 第三步：查看监控状态
- 绿色：服务正常
- 红色：服务异常
- 黄色：警告状态

## 🔔 通知设置建议

### 推荐通知方式
1. **邮件通知**：适合重要服务
2. **Webhook**：集成到其他系统
3. **Telegram**：即时通知

### 通知触发条件
- 服务下线时立即通知
- 服务恢复时发送恢复通知
- 连续失败3次后通知

## 📊 监控面板优化

### 分组建议
- **核心服务**：HTTP、HTTPS、SSH
- **应用服务**：Docker、1Panel
- **网络测试**：Ping监控

### 标签使用
- `server:local` - 本机服务
- `type:web` - Web服务
- `critical:high` - 重要服务

## 🛠️ 故障排除

### 常见问题及解决方案

1. **HTTP监控失败**
   - 检查Nginx是否运行：`systemctl status nginx`
   - 检查端口占用：`netstat -tlnp | grep :80`

2. **端口监控失败**
   - 确认服务正在运行
   - 检查防火墙设置
   - 验证端口是否正确

3. **Ping监控失败**
   - 检查网络配置
   - 确认容器网络权限

## 🎉 完成后的效果

设置完成后，您将看到：
- 实时的服务状态监控
- 历史可用性统计
- 响应时间图表
- 故障时的即时通知

现在就开始配置您的监控系统吧！
